// Objects

// const student = {
//     fullName : "<PERSON><PERSON><PERSON>",
//     age : 20,
//     marks : 80,
//     printMarks : function(){
//         console.log(this.marks);          // instead of student.marks
// }

// Prototypes

// const employee = {
//     calcTax() {
//         console.log("Tax is 10%");
//     }
// }

// const karanArjun = {
//     salary : 100000,
//     calcTax() {
//         console.log("Tax is 20%");
//     }
// }

// // const karanArjun2 = {
// //     salary : 100000,
// // }

// // const karanArjun3 = {
// //     salary : 100000,
// // }


// karanArjun.__proto__ = employee;
// karanArjun2.__proto__ = employee;
// karanArjun3.__proto__ = employee;

// Classes

// class ToyotaCar {
//     constructor(brandName , mileage) {
//         console.log("Car created");
//         this.brandName = brandName;
//         this.mileage = mileage;
//     }


//     start() {
//         console.log(" Car started");
//     }

//     stop() {
//         console.log(" Car stopped");
//     }

//     // brandName(brand) {
//     //     console.log(`Brand name is ${brand}`);
//     // }
// }

// let fortuner = new ToyotaCar("Fortuner",20);
// let innova = new ToyotaCar("Innova",15);

// Inheritance

// class Person {

//     constructor() {
//         this.species = "Homo sapiens";
//     }

//     eat() {
//         console.log("Eating");
//     }

//     sleep() {
//         console.log("Sleeping");
//     }

//     work() {
//         console.log("Doing nothing");
//     }
// }

// class Engineer extends Person {

//     work() {                                        // work method overrides the work method of Person class
//         console.log("Coding");
//     }
// }

// class Doctor extends Person {

//     work() {                                        // work method overrides the work method of Person class
//         console.log("Healing");
//     }
// }

// const e1 = new Engineer();
// const d1 = new Doctor();

// Super keyword

// class Person {

//     constructor(name) {
//         this.species = "Homo sapiens";
//         this.name = name;
//     }

//     eat() {
//         console.log("Eating");
//     }
  
// }

// class Engineer extends Person {
//     constructor(name) {
//         super(name);
//     }

//     work() {        
//         super.eat();                                
//         console.log("Coding");
//     }
// }

// const e1 = new Engineer("Ajil");

// Error Handling

let a = 10;
let b = 40;

console.log("a+b =", a+b);
console.log("a+b =", a+b);
console.log("a+b =", a+b);

try{
    console.log("a+b =", a+c);
}
catch(err) {
    console.log("Error:",err);
}


console.log("a+b =", a-b);
console.log("a+b =", a-b);
console.log("a+b =", a-b);
console.log("a+b =", a-b);
