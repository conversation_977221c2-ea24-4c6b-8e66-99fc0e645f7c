// Event Handling

// let btn1 = document.querySelector("#btn1");

// btn1.onclick = () => {
//     console.log("Button clicked!");
//     let a = 50;
//     a++;
//     console.log(a);
// }

// let btn2 = document.querySelector("#btn2");

// btn2.ondblclick = () => {
//     console.log("Button double clicked!");
// }

// let box = document.querySelector(".box");

// box.onmouseover = () => {
//     console.log("You are inside the div!");
// }

// Event Object

// let btn1 = document.querySelector("#btn1");

// btn1.onclick = (evt) => {
//     console.log("Button clicked!");
//     console.log(evt);
//     console.log(evt.type);
//     console.log(evt.target);
//     console.log(evt.clientX , evt.clientY);
// }

// let box = document.querySelector(".box");

// box.onmouseover = (evt) => {
//     console.log("You are inside the div!");
//     console.log(evt);
//     console.log(evt.type);
//     console.log(evt.target);
//     console.log(evt.clientX , evt.clientY);
// }

// Event Listeners

let btn1 = document.querySelector("#btn1");

const handler1 = () => {
    console.log("Button clicked! - handler1");
}

btn1.addEventListener("click" , handler1);

btn1.addEventListener("click" , () => {
    console.log("Button clicked! - handler2");
})

btn1.addEventListener("click" , () => {
    console.log("Button clicked! - handler3");
})

btn1.addEventListener("click" , () => {
    console.log("Button clicked! - handler4");
})

btn1.removeEventListener("click" , handler1);  // the callback reference should be same to remove



