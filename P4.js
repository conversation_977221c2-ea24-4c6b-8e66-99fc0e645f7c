// Q1

// let marks = [85,97,44,37,76,60];
// let sum = 0;

// for(let val of marks){
//     sum += val;
// }
// console.log(`Average marks = ${sum/marks.length}`);

// Q2

// for-of loop

// let items = [250,645,300,900,50];
// console.log(items);
// let i = 0;

// for(let val of items){
//     let offer = val/10;
//     items[i] = items[i] - offer;
//     console.log(`Price after offer = ${items[i]}`)
//     i++;
// }

// console.log(items);

// for loop

// let items = [250,645,300,900,50];
// console.log(items);

// for(let i = 0; i<items.length; i++){
//     let offer = items[i]/10;
//     items[i] -= offer;
//     console.log(`Price after offer = ${items[i]}`);
// }

// console.log(items);

// Q3

let arr = ["Bloomberg","Microsoft","Uber","Google","IBM","Netflix"];

arr.shift();

arr.splice(1,1,"<PERSON>la");

arr.push("Amazon");

console.log(arr);