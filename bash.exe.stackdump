Stack trace:
Frame         Function      Args
0007FFFFB740  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFFA640) msys-2.0.dll+0x1FE8E
0007FFFFB740  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA18) msys-2.0.dll+0x67F9
0007FFFFB740  000210046832 (000210286019, 0007FFFFB5F8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFB740  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFB740  000210068E24 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFBA20  00021006A225 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFC66110000 ntdll.dll
7FFC65230000 KERNEL32.DLL
7FFC63200000 KERNELBASE.dll
7FFC63EC0000 USER32.dll
7FFC63700000 win32u.dll
7FFC64420000 GDI32.dll
7FFC63960000 gdi32full.dll
7FFC63660000 msvcp_win.dll
7FFC63A90000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFC65170000 advapi32.dll
7FFC64DC0000 msvcrt.dll
7FFC64460000 sechost.dll
7FFC63930000 bcrypt.dll
7FFC65300000 RPCRT4.dll
7FFC629C0000 CRYPTBASE.DLL
7FFC63D20000 bcryptPrimitives.dll
7FFC66090000 IMM32.DLL
