<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <link rel="stylesheet" href="Currency-Converter.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
</head>
<body>
    <div class="container">
        <h2>Currency Converter</h2>
        <form>
            <div class="amount">
                <p>Enter Amount</p>
                <input value="100" type="text" />
            </div>
            <div class="dropdown">
                <div class="From">
                    <p>From</p>
                    <div class="select-container">
                        <img src="https://flagsapi.com/US/flat/64.png">
                        <select name="from">
                    
                        </select>
                    </div>
                </div>
                <i class="fa-solid fa-arrow-right-arrow-left"></i>
                <div class="to">
                    <p>To</p>
                    <div class="select-container">
                        <img src="https://flagsapi.com/IN/flat/64.png">
                        <select name="to">
                
                        </select>
                    </div>
                </div>
            </div>
            <div class="msg">1USD = 80INR</div>
            <button>Get Exchange Rate</button>
        </form>
    </div>



    <script src="Currency-Converter.js"></script>
</body>
</html>