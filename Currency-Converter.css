* {
    margin: 0;
    padding: 0;
}

body {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background-color: #D0D6B5;
}

.container {
    background-color: #fff;
    padding: 3rem;
    border-radius: 1rem;
    min-height: 45vh;
    min-width: 40vh;
}

form {
    margin: 2rem 0 1rem 0;
}

form input,select,button {
    width: 100%;
    border-radius: 1rem;
    border: none;
    outline: none;
} 

form input {
    border: 1px solid lightgray;
    font-size: 1rem;
    height: 3rem;
    padding-left: 0.5rem;
}

.dropdown {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 2rem;
}

.dropdown i {
    font-size: 2rem;
    margin-top: 1rem;
}

.select-container img {
    max-width: 2rem;
}

.select-container{
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 0.5rem;
    width: 6rem;
    border: 1px solid lightgray;
}

.select-container select {
    font-size: 1rem;
    width: auto;
}

.msg {
    margin: 2rem 0 2rem 0;
}

form button {
    height: 3rem;
    font-size: 1rem;
    background-color: #987284;
    color: #fff;
    cursor: pointer;
}