// console.dir(document);
// console.dir(document.body.childNodes[1])
// console.log(document.body)

//DOM Manipulation

// let button = document.getElementById("myId");
// let heading = document.getElementsByClassName("myHeading");

// console.dir(button);
// console.log(button);

// console.dir(heading);
// console.log(heading);

// let para = document.getElementsByTagName("p");

// console.dir(para);
// console.log(para);

//DOM Query Selectors

// let firstEl = document.querySelector("p");
// console.dir(firstEl);

// let allEl = document.querySelectorAll("p");
// console.dir(allEl);

// let element = document.querySelectorAll(".myHeading");
// console.dir(element);

// let element2 = document.querySelector("#myId");
// console.dir(element2);

//DOM properties

// let element = document.querySelector("#myId");
// console.dir(element.tagName);

// let element2 = document.querySelector("p");
// console.dir(element2.tagName);

// let element = document.querySelector("div");
// console.dir(element.innerText);
// console.dir(element.innerHTML);

// let element = document.querySelector(".newHeading");
// // console.dir(element.innerText = "Vegetables");
// console.dir(element.innerHTML = "<i>Vegetables</i>");

// let heading = document.querySelector("h2");
// console.dir(heading.innerText);
// console.dir(heading.textContent);

//  study MDN Docs

// Node:firstChild Property

