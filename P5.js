// Q1

// function countVowels (str){
//     let count = 0;
//     str = str.toLowerCase();
//     for(char of str){
//         if(char === "a" || char === "e" || char === "i" || char === "o" || char === "u"){
//             count++;
//         }
        
//     }
//     return count;
// }

// Q2

// const countVowels = (str) => {
//     str = str.toLowerCase();
//     let count = 0;
//     for(char of str){
//         if(char === "a" || char === "e" || char === "i" || char === "o" || char === "u"){
//             count++;
//         }
//     }
//     return count;
// }

// console.log(countVowels("Ajil Nair"));
// console.log(countVowels("xyz"));

// Q3

// let arr = [1,2,3,4,5];

// arr.forEach((val) => {
//     console.log(val**2);
// })

// Q4

// let marks = [85,97,44,37,91,60];

// let gradeO = marks.filter((val) => {
//     return val > 90;
// })

// console.log(gradeO);

// Q5

let n = prompt("Enter a number:");
let arr = [];

for(let i = 1 ; i<=n; i++){
    arr[i-1] = i;
}

console.log(arr);

let sum = arr.reduce((res , curr) =>{
    return res + curr;
})

console.log("sum = ",sum);

let fact = arr.reduce((res , curr) => {
    return res * curr;
})

console.log("Factorial = ",fact);