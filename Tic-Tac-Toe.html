<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tic-Tac-Toe Game</title>
    <link rel="stylesheet" href="Tic-Tac-Toe.css">
</head>
<body>
    <main>
        <div class="msg-container hide">
            <p id="msg">Winner</p>
            <button id="newBtn">New Game</button>
        </div>
        <h1>Tic-Tac-Toe Game</h1>
        <div class="container">
            <div class="game">
                <button class="box"></button>
                <button class="box"></button>
                <button class="box"></button>
                <button class="box"></button>
                <button class="box"></button>
                <button class="box"></button>
                <button class="box"></button>
                <button class="box"></button>
                <button class="box"></button>
            </div>
        </div>
        <button id="resetBtn">Reset Game</button>
    </main>


    <script src="Tic-Tac-Toe.js"></script>
</body>
</html>