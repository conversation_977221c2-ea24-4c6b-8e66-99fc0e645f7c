const URL = "https://api.thecatapi.com/v1/images/search?limit=10";
const imgContainer = document.querySelector("#imgContainer");
const btn = document.querySelector("#btn");

// async await 

const getImgs = async () => {
    console.log("fetching data..........");
    let response = await fetch(URL);
    console.log(response);
    // console.log(response.status);
    let data = await response.json()
    console.log(data);
    console.log(data[3].url);
    imgContainer.innerText = data[3].url;
}

// Promise chaining

// function getImgs() {
//     fetch(URL)
//         .then((response) => {
//         return response.json();
//         })
//         .then((data) => {
//             console.log(data);
//             imgContainer.innerText = data[3].url;
//         })
// }


btn.addEventListener("click" , getImgs);

// HW

// Http request methods

// Http response status codes

// Sending post request

