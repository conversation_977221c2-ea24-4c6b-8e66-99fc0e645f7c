// Types of nodes in DOM tree

// 1. Element node     // IMP
// 2. Text node
// 3. Comment node

// Node:firstChild Property

// const elm = document.getElementById("div-1");
// console.log(elm.firstChild.nodeName);

// Node:lastChild Property

// const elm = document.getElementById("div-1");
// console.log(elm.lastChild.nodeName);

// Node:childNodes Property

// const elm = document.getElementById("div-1");
// console.log(elm.childNodes);

// appendChild() 

// removeChild()
