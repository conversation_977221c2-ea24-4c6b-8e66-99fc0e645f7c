// Asnchronous Programming

// console.log("one");
// console.log("two");

// setTimeout( () => {
//     console.log("hello");
// } , 4000);

// console.log("three");
// console.log("four");

// Callbacks

// function getData(dataId , getNextData) {
//     setTimeout(() => {
//         console.log("data" , dataId);
//         if (getNextData){
//             getNextData();
//         }
//     } , 2000);
// }

// // callback hell 

// getData(1 , () => {
//     console.log("fetching data 2 .....");
//     getData(2 , () => {
//         console.log("fetching data 3 .....");
//         getData(3 , () => {
//             console.log("fetching data 4 .....");
//             getData(4);
//         });
//     })
// });

// Promises

// let promise = new Promise((resolve , reject) => {
//     console.log("promise created");
//     // resolve("success");
//     reject("some error occured");
// })

// function getData(dataId , getNextData) {
//     return new Promise((resolve , reject) => {
//         setTimeout(() => {
//             // console.log("data" , dataId);
//             // resolve("success");
//             reject("some error occured");
//             if (getNextData){
//                 getNextData();
//             }
//         } , 10000);
//     });
// }

// .then() and .catch()

// const getPromise = () => {
//     return new Promise((resolve , reject) => {
//         console.log("promise created");
//         // resolve("success");
//         reject("some network error occured");
//     })
// }

// let promise = getPromise();

// promise.then( (res) => {
//     console.log("promise fulfilled" , res);
// }) 

// promise.catch( (err) => {
//     console.log("promise rejected" , err);
// })

// Promise Chaining

// 1

// const async1 = () => {
//     return new Promise((resolve , reject) => {
//         setTimeout( () => {
//             console.log("data1");
//             resolve("success");
//         }, 4000);
//     })
// }

// const async2 = () => {
//     return new Promise((resolve , reject) => {
//         setTimeout( () => {
//             console.log("data2");
//             resolve("success");
//         }, 4000);
//     })
// }


// console.log("fetching data1........");
// async1().then( (res) => {
//     console.log(res);
//     console.log("fetching data2........");
//     async2().then( (res) => {
//         console.log(res);
//     })
// })

// 2

// function getData(dataId) {
//     return new Promise((resolve , reject) => {
//         setTimeout(() => {
//             console.log("data" , dataId);
//             resolve("success");
//     } , 2000);
//     })
// }

// console.log("fetching data 1 .......");
// getData(1)
//     .then((res) => {
//         console.log("fetching data 2 .......");
//         return getData(2);
//     })
//     .then((res) => {
//         console.log("fetching data 3 .......");
//         return getData(3);
//     })
//     .then((res) => {
//         console.log(res);
//     })

// Async Await

// 1

// function api() {
//     return new Promise((resolve , reject) => {
//         setTimeout(() => {
//             console.log("weather data");
//             resolve(200);
//         }, 2000)
//     })
// }

// async function getWeatherData() {
//     await api(); //1st
//     await api(); //2nd
// } 

// 2

// function getData(dataId) {
//     return new Promise((resolve , reject) => {
//         setTimeout(() => {
//             console.log("data" , dataId);
//             resolve("success");
//     } , 2000);
//     })
// }

// async function getAllData() {
//     console.log("fetching data 1 .......");
//     await getData(1);
//     console.log("fetching data 2 .......");
//     await getData(2);
//     console.log("fetching data 3 .......");
//     await getData(3);
//     console.log("fetching data 4 .......");
//     await getData(4);
// }

// IIFE

function getData(dataId) {
    return new Promise((resolve , reject) => {
        setTimeout(() => {
            console.log("data" , dataId);
            resolve("success");
    } , 2000);
    })
}

(async function getAllData() {
    console.log("fetching data 1 .......");
    await getData(1);
    console.log("fetching data 2 .......");
    await getData(2);
    console.log("fetching data 3 .......");
    await getData(3);
    console.log("fetching data 4 .......");
    await getData(4);
})();