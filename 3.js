//Loops

//for loop

// let sum = 0;
// for(let i = 0; i<=10; i++){
//     sum = sum + i;
// }
// console.log("sum =",sum);

//while loop

// let i = 1;
// while(i<=10){
//     console.log("i=",i);
//     i++;
// }

//do-while loop

// let i = 1;
// do {
//     console.log("Apna College");
//     i++;
// }while(i<=10);

//for-of loop

// let str = "<PERSON><PERSON><PERSON>";
// let size = 0;

// for(let val of str){
//     console.log("val =",val);
//     size++;
// }
// console.log("size =",size);

// for-in loop

// let student = {
//     fullName : "<PERSON><PERSON><PERSON>",
//     age : 20,
//     marks : 80,
//     isGraduated : true,
// }

// for(let key in student){
//     console.log("key =", key, "value =", student[key] );
// }

//Strings

// let str = "Ajil<PERSON>air";

// console.log(str);
// console.log(str.length);
// console.log(str[0]);

//Template Literals

// let obj ={
//     name : "Ajil <PERSON>",
//     age : 20,
//     marks : 80,
//     isGraduated : true,
// }

// console.log(`My name is ${obj.name} and my age is ${obj.age}`)

// let specialStr = `I am Learning JS from Apna College after ${2+3+8*12} months`;
// console.log(specialStr);

//Escape Characters

// let str = "Ajil\nNair";
// console.log(str);
// console.log(str.length);

// let str2 = "Ajil\tNair";
// console.log(str2);
// console.log(str2.length);

//String methods (Strings are immutable)

// let str = "Ajil Nair";
// str = str.toUpperCase();
// console.log(str);
// str = str.toLowerCase();
// console.log(str);

// let str1 = "       Ajil   Nair         ";
// str1 = str1.trim();          //Removes spaces from both ends
// console.log(str1);

// let str = "0123456789";
// str = str.slice(0,3);
// console.log(str);

// let str1 = "ajil";
// let str2 = "nair";
// str = str1.concat(str2);
// str = str1 + str2;
// console.log(str);

// let str = "hellolololo";
// str = str.replace("lo","p");
// str1 = str.replaceAll("lo","p");
// console.log(str);
// console.log(str1);

// let str = "ajil nair";
// str = str.charAt(5);
// console.log(str);

