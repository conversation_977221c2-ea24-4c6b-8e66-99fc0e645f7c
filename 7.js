// Attributes

// get attribute

// let div = document.querySelector(".myDiv");
// console.log(div);

// console.dir(div.getAttribute("class"));     // Provides value

// let para = document.querySelector(".myPara");
// console.log(para);

// console.dir(para.getAttribute("class"));    // Provides value

// set attribute

// let div = document.querySelector(".myDiv");
// div.setAttribute("class","newValue");
// console.log(div);

// let para = document.querySelector(".myPara");
// para.setAttribute("class","newPara");
// console.log(para);

// style attributes

let div = document.querySelector(".myDiv");

div.style.backgroundColor = "red";
div.style.fontSize = "2rem";
div.style.color = "white";
div.style.textAlign = "center";
div.style.padding = "2rem";

// console.log(div.style);

// Insert Elements

// let div = document.querySelector(".myDiv");
let newBtn = document.createElement("button");
console.log(newBtn);

newBtn.innerText = "Click me";
// div.append(newBtn);
// div.prepend(newBtn);
div.after(newBtn);
// div.before(newBtn);

// Create elements

let newHeading = document.createElement("h1");
newHeading.innerHTML = "<i>Hello JS!</i>";

document.querySelector("body").prepend(newHeading);


// Delete Elements
let para = document.querySelector(".myPara");
para.remove();

newHeading.remove();
