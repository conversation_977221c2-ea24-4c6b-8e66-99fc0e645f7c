*{
    margin: 0;
    padding: 0;
    text-align: center;
}

h1{
    background-color: #081b31;
    color: #fff;
    height: 5rem;
    line-height: 5rem;
}

.choices{
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-top: 3rem;
    margin-bottom: 3rem;
}

.choice{
    height: 165px;
    width: 165px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.choice:hover{
    cursor: pointer;
    background-color: #081b31;
}

img{
    height: 150px;
    width: 150px;
    object-fit: cover;
    border-radius: 50%;

}

.score-board{
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 5rem;
    font-size: 3rem;
}

#user-score , #comp-score{
    font-size: 5rem;
    
}

.msg-container{
    margin-top: 3rem;
}

#msg{
    background-color: #081b31;
    color: #fff;
    height: 5rem;
    line-height: 5rem;
    font-size: 2rem;
    display: inline;
    padding: 1rem;
    border-radius: 1rem;
}