//Arrays

// let marks = [12,45,67,23,98];
// console.log(marks);
// console.log(marks.length);  //Property
// console.log(marks[3]);
// marks[3] = 44;
// console.log(marks); //Arrays are mutable

//Looping over an array

//for loop

// let studName = ["ajil","jax","amal","rohit"];

// for(let i = 0; i<studName.length; i++){
//     console.log(studName[i]);
// }

//for-of loop

// let cities = ["Mumbai","Delhi","Kolkata","Chennai"];

// for(let city of cities){
//     console.log(city.toUpperCase());
// }

// Array methods

// let items = ["potato","onoins", "tomato" , 938];
// console.log(items);

// items.push("ginger","garlic","apples");
// console.log(items);

// deletedItem = items.pop();
// console.log(deletedItem);

// console.log(items.toString());

// let marHeroes = ["antman","ironman","thor"];
// let dcHeroes = ["superman","batman","wonder woman"];
// let indHeroes = ["shaktiman","krish","minnal murli"];

// let heroes = marHeroes.concat(dcHeroes,indHeroes);
// console.log(heroes);

// console.log(heroes.shift());

// console.log(heroes.unshift("Spiderman"));
// console.log(heroes);

// Slice

// let arr = [1,2,3,4,5,6,7];
// console.log(arr.slice(2,4));

// Splice

let arr = [1,2,3,4,5,6,7];

//add elememts
arr.splice(3,0,100);
//delete elements
arr.splice(6,1);
//relace elements
arr.splice(5,1,101);

arr.splice(5);      //acts as slice

