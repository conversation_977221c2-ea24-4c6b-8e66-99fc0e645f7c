// Q1

let modeBtn = document.querySelector("button");
let body = document.querySelector("body");
let currMode = "light";

modeBtn.addEventListener("click" , () => {
    if(currMode === "light"){
        currMode = "dark";
        body.classList.remove("light");
        body.classList.add("dark");
    }
    else{
        currMode = "light";
        body.classList.remove("dark");
        body.classList.add("light");
    }

    console.log(currMode);
})

// HW (mouseover)

let para = document.querySelector("p");

para.addEventListener("mouseover" , () =>{
    para.classList.add("highlight");
})

para.addEventListener("mouseout" , () =>{
    para.classList.remove("highlight");
})




