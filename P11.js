// Q1 

let DATA = "secret information";

class User {

    constructor(name , email) {
        this.name = name;
        this.email = email;
    }

    viewData() {
        console.log(`Name: ${this.name} Email: ${this.email}`);
    }

}

// Q2

class Admin extends User {
    constructor(name , email){
        super(name , email);
    }

    editData() {
        DATA = "new secret information";
        console.log(DATA);
    }
}

let user1 = new User("Ajil" , "<EMAIL>");
user1.viewData();
let admin1 = new Admin("Amal" , "<EMAIL>");
admin1.editData();