// Functions

// function sum(x,y){
//     // parameters     //local variables ---> scope
//     sum = x + y;
//     console.log("before return");
//     return sum;
// }

// let val = sum(10,50);  //arguments
// console.log(val);

// Arrow functions

// const arrowSum = (x,y) => {
//     console.log(x + y);
// }

// const arrowMul = (a,b) => {
//     console.log(a * b);
// }

// arrowMul(10,40);

// let printHello = () => {
//     console.log("Hello");
// }

// forEach loop in arrays

// let arr = ["pune","mumbai","delhi","kolkata"];

// arr.forEach((val , idx , arr) => {
//     console.log(val.toUpperCase() , idx , arr);
// }) 

/*Higher order functions

    function as parameter or function as return*/ 

// Map array

// let nums = [1,2,3,4,5,6,7,8,9,10];

// let squares = nums.map((val) => {
//     return val**2;
// })

// console.log(squares);

// filter arrays

// let nums = [1,2,3,4,5,6,7,8,9,10];

// let evenNums  = nums.filter((val) => {
//     return val % 2 === 0;
// })

// console.log(evenNums);

// reduce arrays

// let nums = [1,2,3,4,5,6,7,8,9,10];

// let sum = nums.reduce((res , curr) =>{
//     return res + curr ;
// })

// console.log(sum);

// let nums = [4,23,85,42,5,262,252];

// const greatest = nums.reduce((prev , curr) => {
//     return prev > curr ? prev : curr;
// })

// console.log(greatest);