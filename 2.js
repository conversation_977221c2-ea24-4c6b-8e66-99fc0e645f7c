//operators

//arithmetic operators

// let a = 5;
// let b = 2;

// console.log(a+b);
// console.log(a-b);
// console.log(a*b);
// console.log(a/b);
// console.log(a%b);
// console.log(a**b);
// console.log(a++);
// console.log(a--);
// console.log(++a);
// console.log(--a);

//assignment operators

// let c = 10;
// let d = 5;

// console.log(c += 2);
// console.log(c -= 2);
// console.log(c *= 2);
// console.log(c %= 2);
// console.log(c **= 2);

//comparison operators

// let e = 10;
// let f = 5;

// console.log(e == f);
// console.log(e != f);
// console.log(e > f);
// console.log(e < f);
// console.log(e >= f);
// console.log(e <= f);
// console.log(e === f);
// console.log(e !== f);

//logical operators

// let a = 10;
// let b = 20;
// let c = 30;

// console.log(a<b && b<c);
// console.log(a<b && b>c);
// console.log(a<b || b>c);
// console.log(a>b || b>c);
// console.log(!(a>b));
// console.log(!(b<c));

//Conditional statements

//if-else statement

// let num = 19;

// if (num%2 === 0){
//     console.log("even")
// }else{
//     console.log("odd")
// }

//else-if statement

// let mode = "filter";

// if(mode === "dark"){
//     color = "black";
// }
// else if(mode === "filter"){
//     color = "gray";
// }
// else if(mode === "blue"){
//     color = "blue";
// }
// else{
//     color = "white";
// }

// console.log(color);

//ternary operator (Compact if-else statement)

// let age = 21;
// let result = age>=18 ? "can vote" : "cannot vote"

// console.log(result);

